# Travel Smart - Flight Booking Website

A modern, bilingual (English/German) flight booking website built with Next.js 15, TypeScript, and Tailwind CSS. This application integrates with the Travel Smart backend API that uses Duffel for flight operations.

## Features

- 🌍 **Bilingual Support**: English and German localization
- ✈️ **Flight Search**: Search flights by origin, destination, dates, and passengers
- 📋 **Flight Results**: View and sort flight offers with detailed information
- 💳 **Flight Booking**: Complete booking process with passenger information
- 📱 **Responsive Design**: Mobile-first design with excellent UX
- 🎨 **Modern UI**: Clean and intuitive interface using Tailwind CSS
- 🔄 **Real-time Data**: Integration with Travel Smart backend API

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Internationalization**: next-intl
- **Forms**: React Hook Form
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **Date Picker**: React DatePicker

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Travel Smart backend API running on `http://localhost:8000`

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd travelsmart-front
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` and set your API base URL:
```
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/
│   ├── [locale]/           # Internationalized routes
│   │   ├── page.tsx        # Home page
│   │   ├── flights/        # Flight-related pages
│   │   └── bookings/       # Booking management
│   ├── globals.css         # Global styles
│   └── layout.tsx          # Root layout
├── components/             # Reusable components
│   ├── Header.tsx          # Navigation header
│   ├── FlightSearchForm.tsx
│   ├── FlightResults.tsx
│   ├── BookingForm.tsx
│   ├── Loading.tsx
│   └── ErrorMessage.tsx
├── lib/
│   ├── api.ts             # API client and types
│   └── utils.ts           # Utility functions
└── i18n/
    └── request.ts         # Internationalization config

messages/
├── en.json                # English translations
└── de.json                # German translations
```

## API Integration

The application integrates with the Travel Smart backend API with the following endpoints:

- **Airports**: `GET /flights/airports` - Get all airports
- **Search Airports**: `GET /flights/airports?query=London` - Search airports
- **Airlines**: `GET /flights/airlines` - Get all airlines
- **Search Flights**: `POST /flights/search` - Search for flights
- **Get Search Results**: `GET /flights/searches/{id}` - Get search results
- **Get Offers**: `GET /flights/searches/{id}/offers` - Get flight offers
- **Book Flight**: `POST /flights/book` - Book a flight
- **Get Bookings**: `GET /flights/bookings` - Get user bookings

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Internationalization

The app supports English and German languages. Language switching is available in the header. Translations are stored in the `messages/` directory.

To add a new language:
1. Create a new JSON file in `messages/` (e.g., `fr.json`)
2. Add the locale to the `locales` array in `i18n/request.ts`
3. Update the language switcher in `Header.tsx`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
