{"name": "travelsmart-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.1", "next-intl": "^3.22.4", "axios": "^1.7.9", "react-hook-form": "^7.54.2", "react-datepicker": "^7.5.0", "lucide-react": "^0.468.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "date-fns": "^4.1.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "@eslint/eslintrc": "^3"}}