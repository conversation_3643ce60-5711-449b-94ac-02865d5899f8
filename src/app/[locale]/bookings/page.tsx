'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Header from '@/components/Header';
import Loading from '@/components/Loading';
import ErrorMessage from '@/components/ErrorMessage';
import { bookingsApi } from '@/lib/api';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Calendar, MapPin, Users, CreditCard } from 'lucide-react';

interface Booking {
  id: number;
  reference: string;
  status: string;
  total_amount: string;
  total_currency: string;
  created_at: string;
  passengers: Array<{
    given_name: string;
    family_name: string;
  }>;
  slices: Array<{
    origin: {
      iata_code: string;
      city_name: string;
    };
    destination: {
      iata_code: string;
      city_name: string;
    };
    departure_datetime: string;
  }>;
}

export default function BookingsPage() {
  const t = useTranslations();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      setLoading(true);
      const response = await bookingsApi.getAllBookings();
      setBookings(response.data);
    } catch (err) {
      setError('Error loading bookings');
      console.error('Error loading bookings:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Loading message={t('common.loading')} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ErrorMessage message={error} onRetry={loadBookings} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('navigation.bookings')}
          </h1>
          <p className="text-gray-600">
            Manage your flight bookings and view travel details
          </p>
        </div>

        {bookings.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings yet</h3>
              <p className="text-gray-600 mb-6">
                You haven't made any flight bookings yet. Start by searching for flights.
              </p>
              <a
                href="/en/flights"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Search Flights
              </a>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {bookings.map((booking) => (
              <div key={booking.id} className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Booking #{booking.reference}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Booked on {formatDate(new Date(booking.created_at))}
                      </p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                      {booking.status}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    {/* Route */}
                    <div className="flex items-center">
                      <MapPin className="h-5 w-5 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {booking.slices[0]?.origin.iata_code} → {booking.slices[0]?.destination.iata_code}
                        </div>
                        <div className="text-xs text-gray-600">
                          {booking.slices[0]?.origin.city_name} → {booking.slices[0]?.destination.city_name}
                        </div>
                      </div>
                    </div>

                    {/* Date */}
                    <div className="flex items-center">
                      <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {formatDate(new Date(booking.slices[0]?.departure_datetime))}
                        </div>
                        <div className="text-xs text-gray-600">Departure</div>
                      </div>
                    </div>

                    {/* Passengers */}
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {booking.passengers.length} {booking.passengers.length === 1 ? 'Passenger' : 'Passengers'}
                        </div>
                        <div className="text-xs text-gray-600">
                          {booking.passengers[0]?.given_name} {booking.passengers[0]?.family_name}
                          {booking.passengers.length > 1 && ` +${booking.passengers.length - 1} more`}
                        </div>
                      </div>
                    </div>

                    {/* Total */}
                    <div className="flex items-center">
                      <CreditCard className="h-5 w-5 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(parseFloat(booking.total_amount), booking.total_currency)}
                        </div>
                        <div className="text-xs text-gray-600">Total paid</div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t">
                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                      View Details
                    </button>
                    {booking.status.toLowerCase() === 'confirmed' && (
                      <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                        Cancel Booking
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
