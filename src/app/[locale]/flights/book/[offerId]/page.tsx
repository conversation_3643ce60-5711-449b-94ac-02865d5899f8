'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import BookingForm from '@/components/BookingForm';
import Loading from '@/components/Loading';
import ErrorMessage from '@/components/ErrorMessage';
import { FlightOffer, flightsApi } from '@/lib/api';

interface Props {
  params: {
    locale: string;
    offerId: string;
  };
}

export default function BookingPage({ params }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const [offer, setOffer] = useState<FlightOffer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const offerId = parseInt(params.offerId);

  useEffect(() => {
    loadOffer();
  }, [offerId]);

  const loadOffer = async () => {
    try {
      setLoading(true);
      const response = await flightsApi.getOfferDetails(offerId);
      setOffer(response.data);
    } catch (err) {
      setError(t('errors.searchError'));
      console.error('Error loading offer:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBookingComplete = (bookingId: number) => {
    // Navigate to booking confirmation page
    router.push(`/${locale}/bookings/${bookingId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Loading message={t('common.loading')} />
        </div>
      </div>
    );
  }

  if (error || !offer) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ErrorMessage 
            message={error || 'Offer not found'} 
            onRetry={loadOffer}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            ← {t('common.previous')}
          </button>
        </div>

        <BookingForm 
          offer={offer} 
          onBookingComplete={handleBookingComplete}
        />
      </div>
    </div>
  );
}
