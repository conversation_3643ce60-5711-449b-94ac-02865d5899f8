'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import FlightSearchForm from '@/components/FlightSearchForm';
import { FlightSearchRequest, flightsApi } from '@/lib/api';

export default function FlightsPage() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSearch = async (searchData: FlightSearchRequest) => {
    setLoading(true);
    try {
      const response = await flightsApi.search(searchData);
      const searchId = response.data.search_id;
      
      // Redirect to results page
      router.push(`/${locale}/flights/search/${searchId}`);
    } catch (error) {
      console.error('Search error:', error);
      // Handle error - show toast or error message
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('search.title')}
          </h1>
          <p className="text-gray-600">
            {t('home.subtitle')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <FlightSearchForm onSearch={handleSearch} loading={loading} />
        </div>

        {/* Tips Section */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Travel Tips
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-blue-600 mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Time to Book</h3>
              <p className="text-gray-600 text-sm">
                Book domestic flights 1-3 months in advance and international flights 2-8 months ahead for the best deals.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-blue-600 mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Flexible Dates</h3>
              <p className="text-gray-600 text-sm">
                Flying on weekdays (Tuesday-Thursday) is often cheaper than weekends. Consider adjusting your travel dates.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-blue-600 mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Alternative Airports</h3>
              <p className="text-gray-600 text-sm">
                Check nearby airports for potentially lower fares. Sometimes a short drive can save you hundreds.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
