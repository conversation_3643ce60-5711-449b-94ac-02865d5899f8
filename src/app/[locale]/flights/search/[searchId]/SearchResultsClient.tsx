'use client';

import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import FlightResults from '@/components/FlightResults';
import { FlightOffer } from '@/lib/api';

interface Props {
  searchId: number;
}

export default function SearchResultsClient({ searchId }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();

  const handleSelectFlight = (offer: FlightOffer) => {
    // Navigate to booking page
    router.push(`/${locale}/flights/book/${offer.id}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('results.title')}
          </h1>
          <button
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            ← {t('common.previous')}
          </button>
        </div>

        <FlightResults 
          searchId={searchId} 
          onSelectFlight={handleSelectFlight}
        />
      </div>
    </div>
  );
}
