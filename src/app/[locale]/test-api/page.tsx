'use client';

import { useState } from 'react';
import { airportsApi } from '@/lib/api';

export default function TestApiPage() {
  const [airports, setAirports] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testAirportsApi = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('Testing airports API...');
      const response = await airportsApi.getAll();
      console.log('API Response:', response.data);
      
      if (response.data.success) {
        setAirports(response.data.data.slice(0, 10)); // Show first 10
      } else {
        setError('API returned success: false');
      }
    } catch (err: any) {
      console.error('API Error:', err);
      setError(err.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testSearchApi = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('Testing search API...');
      const response = await airportsApi.search('London');
      console.log('Search Response:', response.data);
      
      if (response.data.success) {
        setAirports(response.data.data);
      } else {
        setError('Search API returned success: false');
      }
    } catch (err: any) {
      console.error('Search API Error:', err);
      setError(err.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">API Test Page</h1>
        
        <div className="space-y-4 mb-8">
          <button
            onClick={testAirportsApi}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Test Get All Airports'}
          </button>
          
          <button
            onClick={testSearchApi}
            disabled={loading}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 ml-4"
          >
            {loading ? 'Loading...' : 'Test Search Airports (London)'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <h3 className="text-red-800 font-medium">Error:</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {airports.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b">
              <h2 className="text-xl font-semibold">Airports ({airports.length})</h2>
            </div>
            <div className="p-4">
              <div className="space-y-4">
                {airports.map((airport) => (
                  <div key={airport.id} className="border rounded-lg p-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Name:</label>
                        <p className="text-gray-900">{airport.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">City:</label>
                        <p className="text-gray-900">{airport.city_name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">IATA:</label>
                        <p className="text-gray-900">{airport.iata_code}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Country:</label>
                        <p className="text-gray-900">{airport.iata_country_code}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
