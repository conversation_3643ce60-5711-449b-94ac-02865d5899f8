'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { User, Mail, Phone, CreditCard } from 'lucide-react';
import { FlightOffer, bookingsApi } from '@/lib/api';
import { formatCurrency, cn } from '@/lib/utils';

interface BookingFormData {
  passengers: Array<{
    given_name: string;
    family_name: string;
    email: string;
    phone: string;
  }>;
}

interface Props {
  offer: FlightOffer;
  onBookingComplete: (bookingId: number) => void;
}

export default function BookingForm({ offer, onBookingComplete }: Props) {
  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<BookingFormData>({
    defaultValues: {
      passengers: [{ given_name: '', family_name: '', email: '', phone: '' }]
    }
  });

  const onSubmit = async (data: BookingFormData) => {
    setLoading(true);
    setError(null);

    try {
      const bookingData = {
        offer_id: offer.id,
        passengers: data.passengers.map(p => ({
          given_name: p.given_name,
          family_name: p.family_name
        })),
        payments: [{
          type: 'balance',
          amount: offer.total_amount,
          currency: offer.total_currency
        }]
      };

      const response = await bookingsApi.book(bookingData);
      onBookingComplete(response.data.id);
    } catch (err) {
      setError(t('errors.bookingError'));
      console.error('Booking error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Flight Summary */}
        <div className="bg-blue-50 p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {t('booking.title')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {offer.slices.map((slice, index) => (
              <div key={index} className="bg-white rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-lg font-semibold">
                    {slice.origin.iata_code} → {slice.destination.iata_code}
                  </span>
                  <span className="text-sm text-gray-600">
                    {new Date(slice.departure_datetime).toLocaleDateString()}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {slice.origin.city} → {slice.destination.city}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 text-right">
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(parseFloat(offer.total_amount), offer.total_currency)}
            </div>
            <div className="text-sm text-gray-600">{t('booking.total')}</div>
          </div>
        </div>

        {/* Booking Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t('booking.passengerInfo')}
            </h3>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('booking.firstName')} *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      {...register('passengers.0.given_name', { required: true })}
                      className={cn(
                        "w-full pl-10 pr-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500",
                        errors.passengers?.[0]?.given_name ? "border-red-300" : "border-gray-300"
                      )}
                    />
                  </div>
                  {errors.passengers?.[0]?.given_name && (
                    <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('booking.lastName')} *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      {...register('passengers.0.family_name', { required: true })}
                      className={cn(
                        "w-full pl-10 pr-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500",
                        errors.passengers?.[0]?.family_name ? "border-red-300" : "border-gray-300"
                      )}
                    />
                  </div>
                  {errors.passengers?.[0]?.family_name && (
                    <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('booking.email')} *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="email"
                      {...register('passengers.0.email', { 
                        required: true,
                        pattern: /^\S+@\S+$/i
                      })}
                      className={cn(
                        "w-full pl-10 pr-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500",
                        errors.passengers?.[0]?.email ? "border-red-300" : "border-gray-300"
                      )}
                    />
                  </div>
                  {errors.passengers?.[0]?.email && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.passengers[0].email.type === 'required' 
                        ? t('errors.required') 
                        : t('errors.invalidEmail')
                      }
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('booking.phone')} *
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="tel"
                      {...register('passengers.0.phone', { required: true })}
                      className={cn(
                        "w-full pl-10 pr-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500",
                        errors.passengers?.[0]?.phone ? "border-red-300" : "border-gray-300"
                      )}
                    />
                  </div>
                  {errors.passengers?.[0]?.phone && (
                    <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Payment Section */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t('booking.payment')}
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center">
                <CreditCard className="h-6 w-6 text-gray-400 mr-3" />
                <span className="text-gray-700">
                  Payment will be processed securely
                </span>
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className={cn(
              "w-full bg-blue-600 text-white py-3 px-6 rounded-md font-medium transition-colors",
              "hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              loading && "opacity-50 cursor-not-allowed"
            )}
          >
            {loading ? t('common.loading') : `${t('booking.bookNow')} - ${formatCurrency(parseFloat(offer.total_amount), offer.total_currency)}`}
          </button>
        </form>
      </div>
    </div>
  );
}
