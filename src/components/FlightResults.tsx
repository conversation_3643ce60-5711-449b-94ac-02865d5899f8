'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Clock, Plane, ArrowRight, Wifi, Coffee, Tv, Star, Shield, Timer, MapPin } from 'lucide-react';
import { FlightOffer, flightsApi } from '@/lib/api';
import { formatCurrency, formatTime, cn } from '@/lib/utils';
import Loading from './Loading';
import ErrorMessage from './ErrorMessage';

interface Props {
  searchId: number;
  onSelectFlight: (offer: FlightOffer) => void;
}

export default function FlightResults({ searchId, onSelectFlight }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const [offers, setOffers] = useState<FlightOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'total_amount' | 'duration' | 'departure'>('total_amount');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    loadOffers();
  }, [searchId, sortBy, sortDirection]);

  const loadOffers = async () => {
    try {
      setLoading(true);
      const response = await flightsApi.getSearchResults(searchId);
      console.log('Search results response:', response.data);

      if (response.data.success) {
        const searchData = response.data.data;
        console.log('Search data:', searchData);
        console.log('Offers:', searchData.offers);
        setOffers(searchData.offers || []);
      } else {
        setError('Failed to load flight offers');
        setOffers([]);
      }
    } catch (err) {
      setError(t('errors.searchError'));
      console.error('Error loading offers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortDirection('asc');
    }
  };

  const formatDuration = (duration: string) => {
    // Assuming duration is in format "PT2H30M"
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    return `${hours}${minutes}`;
  };

  const getStopsText = (segments: any[]) => {
    const stops = segments.length - 1;
    if (stops === 0) return t('results.nonstop');
    if (stops === 1) return t('results.oneStop');
    return `${stops} ${t('results.multipleStops')}`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 animate-pulse">
          <div className="h-8 bg-blue-200 rounded-lg w-1/3 mb-2"></div>
          <div className="h-4 bg-blue-100 rounded w-1/2"></div>
        </div>

        {/* Loading Cards */}
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 animate-pulse">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
                <div>
                  <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                  <div className="h-3 bg-gray-100 rounded w-32"></div>
                </div>
              </div>
              <div className="flex gap-2">
                <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                <div className="h-6 bg-gray-200 rounded-full w-24"></div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-xl p-6">
              <div className="flex justify-between items-center">
                <div className="text-center">
                  <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-20"></div>
                </div>
                <div className="flex-1 flex justify-center">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                </div>
                <div className="text-center">
                  <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-20"></div>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-between items-center">
              <div>
                <div className="h-8 bg-gray-200 rounded w-24 mb-1"></div>
                <div className="h-3 bg-gray-100 rounded w-16"></div>
              </div>
              <div className="h-12 bg-gray-200 rounded-xl w-32"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadOffers} />;
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-12 max-w-md mx-auto">
          <div className="text-6xl mb-6">✈️</div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('search.noResults')}
          </h3>
          <p className="text-gray-600 mb-6">
            We couldn't find any flights matching your criteria. Try adjusting your search parameters.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <div>• Try different dates</div>
            <div>• Check nearby airports</div>
            <div>• Consider flexible travel options</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header & Sort Controls */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              ✈️ {offers.length} {t('search.resultsFound')}
            </h2>
            <p className="text-gray-600">
              Best deals sorted by price • Updated just now
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-gray-700 self-center mr-2">{t('results.sortBy')}:</span>
            <button
              onClick={() => handleSort('total_amount')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'total_amount'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              💰 {t('results.price')}
              {sortBy === 'total_amount' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
            <button
              onClick={() => handleSort('duration')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'duration'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              ⏱️ {t('results.duration')}
              {sortBy === 'duration' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
            <button
              onClick={() => handleSort('departure')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'departure'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              🕐 {t('results.departure')}
              {sortBy === 'departure' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Flight Offers */}
      <div className="space-y-6">
        {offers.map((offer, index) => (
          <div
            key={offer.id}
            className="flight-card group bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-2xl hover:border-blue-200 transition-all duration-300 overflow-hidden opacity-0"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Best Deal Badge */}
            {index === 0 && (
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-center py-2 px-4">
                <span className="text-sm font-semibold">🏆 Best Deal</span>
              </div>
            )}

            <div className="p-6">
              {/* Airline Info */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-lg">
                    {offer.airline.code}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{offer.airline.name}</div>
                    <div className="text-sm text-gray-500">Flight operated by {offer.airline.name}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">
                    ✓ Refundable
                  </div>
                  <div className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-medium">
                    ⚡ Instant Booking
                  </div>
                </div>
              </div>

              {offer.flight_details.slices.map((slice, sliceIndex) => (
                <div key={sliceIndex} className={cn("", sliceIndex > 0 && "mt-8 pt-8 border-t border-gray-100")}>
                  {/* Route Header */}
                  <div className="flex items-center gap-2 mb-4">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700">
                      {slice.origin.city_name} → {slice.destination.city_name}
                    </span>
                    {slice.is_direct && (
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                        Direct Flight
                      </span>
                    )}
                  </div>

                  {/* Main Flight Info */}
                  <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-4">
                    <div className="flex items-center justify-between">
                      {/* Departure */}
                      <div className="text-center flex-1">
                        <div className="text-3xl font-bold text-gray-900 mb-1">
                          {formatTime(new Date(slice.segments[0]?.departing_at))}
                        </div>
                        <div className="text-lg font-semibold text-blue-600 mb-1">
                          {slice.origin.iata_code}
                        </div>
                        <div className="text-sm text-gray-600">
                          {slice.origin.city_name}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {new Date(slice.segments[0]?.departing_at).toLocaleDateString()}
                        </div>
                      </div>

                      {/* Flight Path */}
                      <div className="flex-1 flex flex-col items-center px-4">
                        <div className="flex items-center w-full mb-2">
                          <div className="h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 flex-1"></div>
                          <div className="bg-blue-600 rounded-full p-2 mx-2">
                            <Plane className="h-4 w-4 text-white transform rotate-90" />
                          </div>
                          <div className="h-0.5 bg-gradient-to-r from-blue-600 to-blue-400 flex-1"></div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium text-gray-700 mb-1">
                            {formatDuration(slice.duration)}
                          </div>
                          <div className="text-xs text-gray-500">
                            {slice.is_direct ? (
                              <span className="text-green-600 font-medium">✈️ Direct</span>
                            ) : (
                              <span className="text-orange-600">🔄 {slice.total_stops} stop{slice.total_stops > 1 ? 's' : ''}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Arrival */}
                      <div className="text-center flex-1">
                        <div className="text-3xl font-bold text-gray-900 mb-1">
                          {formatTime(new Date(slice.segments[slice.segments.length - 1]?.arriving_at))}
                        </div>
                        <div className="text-lg font-semibold text-blue-600 mb-1">
                          {slice.destination.iata_code}
                        </div>
                        <div className="text-sm text-gray-600">
                          {slice.destination.city_name}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {new Date(slice.segments[slice.segments.length - 1]?.arriving_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Segments Details */}
                  {slice.segments.length > 1 && (
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <div className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                        <Timer className="h-4 w-4" />
                        Flight Segments
                      </div>
                      <div className="space-y-3">
                        {slice.segments.map((segment, segmentIndex) => (
                          <div key={segmentIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-4">
                              <div className="text-sm font-medium">
                                {segment.origin.iata_code} → {segment.destination.iata_code}
                              </div>
                              <ArrowRight className="h-3 w-3 text-gray-400" />
                              <div className="text-sm text-gray-600">
                                {segment.marketing_carrier.name}
                              </div>
                            </div>
                            <div className="text-xs text-gray-500">
                              {segment.aircraft?.name || 'Aircraft TBD'}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Amenities */}
                  <div className="flex items-center gap-4 mt-4">
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Wifi className="h-3 w-3" />
                      WiFi
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Coffee className="h-3 w-3" />
                      Meals
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Tv className="h-3 w-3" />
                      Entertainment
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Shield className="h-3 w-3" />
                      Travel Insurance
                    </div>
                  </div>
                </div>
              ))}

              {/* Pricing & Action Section */}
              <div className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  {/* Price Breakdown */}
                  <div className="flex-1">
                    <div className="flex items-baseline gap-2 mb-2">
                      <div className="text-3xl font-bold text-blue-600">
                        {formatCurrency(parseFloat(offer.pricing.total_amount), offer.pricing.total_currency)}
                      </div>
                      <div className="text-sm text-gray-500">per person</div>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex justify-between">
                        <span>Base fare:</span>
                        <span>{formatCurrency(parseFloat(offer.pricing.base_amount), offer.pricing.base_currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Taxes & fees:</span>
                        <span>{formatCurrency(parseFloat(offer.pricing.tax_amount), offer.pricing.tax_currency)}</span>
                      </div>
                    </div>
                    <div className="mt-3 flex items-center gap-2 text-xs text-green-600">
                      <Star className="h-3 w-3 fill-current" />
                      <span>Free cancellation within 24 hours</span>
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="lg:text-right">
                    <button
                      onClick={() => onSelectFlight(offer)}
                      className="w-full lg:w-auto bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg shadow-blue-600/25 hover:shadow-xl hover:shadow-blue-600/30 transition-all duration-300 transform hover:scale-105"
                    >
                      ✈️ {t('results.selectFlight')}
                    </button>
                    <div className="text-xs text-gray-500 mt-2 lg:text-right">
                      Price locked for 10 minutes
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
