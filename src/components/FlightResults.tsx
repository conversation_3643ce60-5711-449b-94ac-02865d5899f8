'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Clock, Plane, ArrowRight, Wifi, Coffee, Tv, Star, Shield, Timer, MapPin } from 'lucide-react';
import { FlightOffer, flightsApi } from '@/lib/api';
import { formatCurrency, formatTime, cn } from '@/lib/utils';
import Loading from './Loading';
import ErrorMessage from './ErrorMessage';

interface Props {
  searchId: number;
  onSelectFlight: (offer: FlightOffer) => void;
}

export default function FlightResults({ searchId, onSelectFlight }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const [offers, setOffers] = useState<FlightOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'total_amount' | 'duration' | 'departure'>('total_amount');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    loadOffers();
  }, [searchId, sortBy, sortDirection]);

  const loadOffers = async () => {
    try {
      setLoading(true);
      const response = await flightsApi.getSearchResults(searchId);
      console.log('Search results response:', response.data);

      if (response.data.success) {
        const searchData = response.data.data;
        console.log('Search data:', searchData);
        console.log('Offers:', searchData.offers);
        setOffers(searchData.offers || []);
      } else {
        setError('Failed to load flight offers');
        setOffers([]);
      }
    } catch (err) {
      setError(t('errors.searchError'));
      console.error('Error loading offers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortDirection('asc');
    }
  };

  const formatDuration = (duration: string) => {
    // Assuming duration is in format "PT2H30M"
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    return `${hours}${minutes}`;
  };

  const getStopsText = (segments: any[]) => {
    const stops = segments.length - 1;
    if (stops === 0) return t('results.nonstop');
    if (stops === 1) return t('results.oneStop');
    return `${stops} ${t('results.multipleStops')}`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 animate-pulse">
          <div className="h-8 bg-blue-200 rounded-lg w-1/3 mb-2"></div>
          <div className="h-4 bg-blue-100 rounded w-1/2"></div>
        </div>

        {/* Loading Cards */}
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-2xl shadow-lg border border-gray-100 p-4 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                <div>
                  <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-20"></div>
                </div>
              </div>

              <div className="flex items-center gap-4 flex-1 justify-center">
                <div className="text-center">
                  <div className="h-6 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-8"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="text-center">
                  <div className="h-6 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-8"></div>
                </div>
              </div>

              <div className="text-right">
                <div className="h-6 bg-gray-200 rounded w-20 mb-1"></div>
                <div className="h-3 bg-gray-100 rounded w-12 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadOffers} />;
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-12 max-w-md mx-auto">
          <div className="text-6xl mb-6">✈️</div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('search.noResults')}
          </h3>
          <p className="text-gray-600 mb-6">
            We couldn't find any flights matching your criteria. Try adjusting your search parameters.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <div>• Try different dates</div>
            <div>• Check nearby airports</div>
            <div>• Consider flexible travel options</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header & Sort Controls */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              ✈️ {offers.length} {t('search.resultsFound')}
            </h2>
            <p className="text-gray-600">
              Best deals sorted by price • Updated just now
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-gray-700 self-center mr-2">{t('results.sortBy')}:</span>
            <button
              onClick={() => handleSort('total_amount')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'total_amount'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              💰 {t('results.price')}
              {sortBy === 'total_amount' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
            <button
              onClick={() => handleSort('duration')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'duration'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              ⏱️ {t('results.duration')}
              {sortBy === 'duration' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
            <button
              onClick={() => handleSort('departure')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'departure'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              🕐 {t('results.departure')}
              {sortBy === 'departure' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Flight Offers */}
      <div className="space-y-6">
        {offers.map((offer, index) => (
          <div
            key={offer.id}
            className="flight-card group bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-2xl hover:border-blue-200 transition-all duration-300 overflow-hidden opacity-0"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Best Deal Badge */}
            {index === 0 && (
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-center py-1 px-4">
                <span className="text-xs font-semibold">🏆 Best Deal</span>
              </div>
            )}

            <div className="p-4">

              {offer.flight_details.slices.map((slice, sliceIndex) => (
                <div key={sliceIndex} className={cn("", sliceIndex > 0 && "mt-4 pt-4 border-t border-gray-100")}>
                  {/* Compact Flight Info */}
                  <div className="flex items-center justify-between">
                    {/* Airline & Flight Info */}
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm overflow-hidden">
                        {slice.segments[0]?.marketing_carrier.logo_symbol_url ? (
                          <img
                            src={slice.segments[0].marketing_carrier.logo_symbol_url}
                            alt={offer.airline.name}
                            className="w-full h-full object-contain"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling!.style.display = 'flex';
                            }}
                          />
                        ) : null}
                        <div className={`w-full h-full flex items-center justify-center ${slice.segments[0]?.marketing_carrier.logo_symbol_url ? 'hidden' : 'flex'}`}>
                          {offer.airline.code}
                        </div>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 text-sm">{offer.airline.name}</div>
                        <div className="text-xs text-gray-500">
                          Flight {slice.segments[0]?.marketing_carrier.iata_code}{slice.segments[0]?.marketing_carrier_flight_number || '1234'}
                        </div>
                      </div>
                    </div>

                    {/* Flight Route */}
                    <div className="flex items-center gap-4 flex-1 justify-center">
                      {/* Departure */}
                      <div className="text-center">
                        <div className="text-xl font-bold text-gray-900">
                          {formatTime(new Date(slice.segments[0]?.departing_at))}
                        </div>
                        <div className="text-sm font-semibold text-blue-600">
                          {slice.origin.iata_code}
                        </div>
                      </div>

                      {/* Flight Path */}
                      <div className="flex flex-col items-center px-3">
                        <div className="flex items-center">
                          <div className="h-px bg-gray-300 w-8"></div>
                          <Plane className="h-3 w-3 text-gray-400 mx-1 transform rotate-90" />
                          <div className="h-px bg-gray-300 w-8"></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {formatDuration(slice.duration)}
                        </div>
                        <div className="text-xs">
                          {slice.is_direct ? (
                            <span className="text-green-600">Direct</span>
                          ) : (
                            <span className="text-orange-600">{slice.total_stops} stop</span>
                          )}
                        </div>
                      </div>

                      {/* Arrival */}
                      <div className="text-center">
                        <div className="text-xl font-bold text-gray-900">
                          {formatTime(new Date(slice.segments[slice.segments.length - 1]?.arriving_at))}
                        </div>
                        <div className="text-sm font-semibold text-blue-600">
                          {slice.destination.iata_code}
                        </div>
                      </div>
                    </div>

                    {/* Price & Action */}
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {formatCurrency(parseFloat(offer.pricing.total_amount), offer.pricing.total_currency)}
                      </div>
                      <div className="text-xs text-gray-500 mb-2">per person</div>
                      <button
                        onClick={() => onSelectFlight(offer)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        Select
                      </button>
                    </div>
                  </div>

                  {/* Additional Info - Only for connecting flights */}
                  {slice.segments.length > 1 && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>Stops: {slice.segments.map(s => s.destination.iata_code).slice(0, -1).join(', ')}</span>
                        <span>•</span>
                        <span>Aircraft: {slice.segments[0]?.aircraft?.name || 'Various'}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
