'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Clock, Plane, ArrowRight } from 'lucide-react';
import { FlightOffer, flightsApi } from '@/lib/api';
import { formatCurrency, formatTime, cn } from '@/lib/utils';
import Loading from './Loading';
import ErrorMessage from './ErrorMessage';

interface Props {
  searchId: number;
  onSelectFlight: (offer: FlightOffer) => void;
}

export default function FlightResults({ searchId, onSelectFlight }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const [offers, setOffers] = useState<FlightOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'total_amount' | 'duration' | 'departure'>('total_amount');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    loadOffers();
  }, [searchId, sortBy, sortDirection]);

  const loadOffers = async () => {
    try {
      setLoading(true);
      const response = await flightsApi.getSearchOffers(searchId, sortBy, sortDirection);
      setOffers(response.data);
    } catch (err) {
      setError(t('errors.searchError'));
      console.error('Error loading offers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortDirection('asc');
    }
  };

  const formatDuration = (duration: string) => {
    // Assuming duration is in format "PT2H30M"
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    return `${hours}${minutes}`;
  };

  const getStopsText = (segments: any[]) => {
    const stops = segments.length - 1;
    if (stops === 0) return t('results.nonstop');
    if (stops === 1) return t('results.oneStop');
    return `${stops} ${t('results.multipleStops')}`;
  };

  if (loading) {
    return <Loading message={t('common.loading')} />;
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadOffers} />;
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 text-lg">{t('search.noResults')}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Sort Controls */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-wrap gap-4 items-center">
          <span className="text-gray-700 font-medium">{t('results.sortBy')}:</span>
          <button
            onClick={() => handleSort('total_amount')}
            className={cn(
              "px-3 py-1 rounded-md text-sm font-medium transition-colors",
              sortBy === 'total_amount'
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            {t('results.price')}
            {sortBy === 'total_amount' && (
              <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          <button
            onClick={() => handleSort('duration')}
            className={cn(
              "px-3 py-1 rounded-md text-sm font-medium transition-colors",
              sortBy === 'duration'
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            {t('results.duration')}
            {sortBy === 'duration' && (
              <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          <button
            onClick={() => handleSort('departure')}
            className={cn(
              "px-3 py-1 rounded-md text-sm font-medium transition-colors",
              sortBy === 'departure'
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            {t('results.departure')}
            {sortBy === 'departure' && (
              <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
        </div>
        <div className="mt-2 text-sm text-gray-600">
          {offers.length} {t('search.resultsFound')}
        </div>
      </div>

      {/* Flight Offers */}
      <div className="space-y-4">
        {offers.map((offer) => (
          <div
            key={offer.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              {offer.slices.map((slice, sliceIndex) => (
                <div key={sliceIndex} className={cn("", sliceIndex > 0 && "mt-6 pt-6 border-t")}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                          {formatTime(new Date(slice.departure_datetime))}
                        </div>
                        <div className="text-sm text-gray-600">
                          {slice.origin.iata_code}
                        </div>
                        <div className="text-xs text-gray-500">
                          {slice.origin.city_name}
                        </div>
                      </div>

                      <div className="flex-1 flex items-center justify-center">
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-2 mb-1">
                            <div className="h-px bg-gray-300 flex-1"></div>
                            <Plane className="h-4 w-4 text-gray-400" />
                            <div className="h-px bg-gray-300 flex-1"></div>
                          </div>
                          <div className="text-sm text-gray-600">
                            {formatDuration(slice.duration)}
                          </div>
                          <div className="text-xs text-gray-500">
                            {getStopsText(slice.segments)}
                          </div>
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                          {formatTime(new Date(slice.arrival_datetime))}
                        </div>
                        <div className="text-sm text-gray-600">
                          {slice.destination.iata_code}
                        </div>
                        <div className="text-xs text-gray-500">
                          {slice.destination.city_name}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Segments Details */}
                  {slice.segments.length > 1 && (
                    <div className="mt-4 space-y-2">
                      {slice.segments.map((segment, segmentIndex) => (
                        <div key={segmentIndex} className="flex items-center text-sm text-gray-600">
                          <span className="w-16">{segment.origin.iata_code}</span>
                          <ArrowRight className="h-3 w-3 mx-2" />
                          <span className="w-16">{segment.destination.iata_code}</span>
                          <span className="ml-4">{segment.marketing_carrier.name}</span>
                          <span className="ml-4">{segment.aircraft.name}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}

              <div className="mt-6 flex items-center justify-between pt-4 border-t">
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(parseFloat(offer.total_amount), offer.total_currency)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {t('results.total')}
                  </div>
                </div>
                <button
                  onClick={() => onSelectFlight(offer)}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
                >
                  {t('results.selectFlight')}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
