'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { Search, MapPin, Calendar, Users, ArrowUpDown } from 'lucide-react';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { airportsApi, Airport, FlightSearchRequest } from '@/lib/api';
import { cn } from '@/lib/utils';

interface FlightSearchFormData {
  origin: string;
  destination: string;
  departureDate: Date;
  returnDate?: Date;
  adults: number;
  children: number;
  infants: number;
  cabinClass: 'economy' | 'premium_economy' | 'business' | 'first';
  tripType: 'oneWay' | 'roundTrip';
}

interface Props {
  onSearch: (searchData: FlightSearchRequest) => void;
  loading?: boolean;
}

export default function FlightSearchForm({ onSearch, loading = false }: Props) {
  const t = useTranslations();
  const [airports, setAirports] = useState<Airport[]>([]);
  const [originSuggestions, setOriginSuggestions] = useState<Airport[]>([]);
  const [destinationSuggestions, setDestinationSuggestions] = useState<Airport[]>([]);
  const [showOriginSuggestions, setShowOriginSuggestions] = useState(false);
  const [showDestinationSuggestions, setShowDestinationSuggestions] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FlightSearchFormData>({
    defaultValues: {
      adults: 1,
      children: 0,
      infants: 0,
      cabinClass: 'economy',
      tripType: 'roundTrip',
      departureDate: new Date(),
    }
  });

  const tripType = watch('tripType');
  const departureDate = watch('departureDate');
  const returnDate = watch('returnDate');

  useEffect(() => {
    // Load airports on component mount
    airportsApi.getAll()
      .then(response => setAirports(response.data))
      .catch(console.error);
  }, []);

  const searchAirports = async (query: string, type: 'origin' | 'destination') => {
    if (query.length < 2) {
      if (type === 'origin') setOriginSuggestions([]);
      else setDestinationSuggestions([]);
      return;
    }

    try {
      const response = await airportsApi.search(query);
      const suggestions = response.data.slice(0, 5);
      
      if (type === 'origin') {
        setOriginSuggestions(suggestions);
        setShowOriginSuggestions(true);
      } else {
        setDestinationSuggestions(suggestions);
        setShowDestinationSuggestions(true);
      }
    } catch (error) {
      console.error('Error searching airports:', error);
    }
  };

  const selectAirport = (airport: Airport, type: 'origin' | 'destination') => {
    setValue(type, airport.iata_code);
    if (type === 'origin') {
      setShowOriginSuggestions(false);
    } else {
      setShowDestinationSuggestions(false);
    }
  };

  const swapAirports = () => {
    const origin = watch('origin');
    const destination = watch('destination');
    setValue('origin', destination);
    setValue('destination', origin);
  };

  const onSubmit = (data: FlightSearchFormData) => {
    const searchRequest: FlightSearchRequest = {
      passengers: [
        ...Array(data.adults).fill({ type: 'adult' }),
        ...Array(data.children).fill({ type: 'child' }),
        ...Array(data.infants).fill({ type: 'infant' }),
      ],
      slices: [
        {
          origin: data.origin,
          destination: data.destination,
          departure_date: data.departureDate.toISOString().split('T')[0],
        },
        ...(data.tripType === 'roundTrip' && data.returnDate ? [{
          origin: data.destination,
          destination: data.origin,
          departure_date: data.returnDate.toISOString().split('T')[0],
        }] : [])
      ],
      cabin_class: data.cabinClass,
    };

    onSearch(searchRequest);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Trip Type */}
        <div className="flex space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              value="roundTrip"
              {...register('tripType')}
              className="mr-2"
            />
            {t('search.roundTrip')}
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              value="oneWay"
              {...register('tripType')}
              className="mr-2"
            />
            {t('search.oneWay')}
          </label>
        </div>

        {/* Origin and Destination */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative">
          {/* Origin */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.from')}
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('search.fromPlaceholder')}
                {...register('origin', { required: true })}
                onChange={(e) => searchAirports(e.target.value, 'origin')}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              {showOriginSuggestions && originSuggestions.length > 0 && (
                <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1">
                  {originSuggestions.map((airport) => (
                    <button
                      key={airport.id}
                      type="button"
                      onClick={() => selectAirport(airport, 'origin')}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 border-b last:border-b-0"
                    >
                      <div className="font-medium">{airport.name}</div>
                      <div className="text-sm text-gray-500">{airport.city}, {airport.country} ({airport.iata_code})</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Swap Button */}
          <div className="absolute left-1/2 top-8 transform -translate-x-1/2 z-10 hidden md:block">
            <button
              type="button"
              onClick={swapAirports}
              className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
            >
              <ArrowUpDown className="h-4 w-4" />
            </button>
          </div>

          {/* Destination */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.to')}
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('search.toPlaceholder')}
                {...register('destination', { required: true })}
                onChange={(e) => searchAirports(e.target.value, 'destination')}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              {showDestinationSuggestions && destinationSuggestions.length > 0 && (
                <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1">
                  {destinationSuggestions.map((airport) => (
                    <button
                      key={airport.id}
                      type="button"
                      onClick={() => selectAirport(airport, 'destination')}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 border-b last:border-b-0"
                    >
                      <div className="font-medium">{airport.name}</div>
                      <div className="text-sm text-gray-500">{airport.city}, {airport.country} ({airport.iata_code})</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('search.departureDate')}
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <DatePicker
                selected={departureDate}
                onChange={(date) => setValue('departureDate', date!)}
                minDate={new Date()}
                dateFormat="dd/MM/yyyy"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {tripType === 'roundTrip' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('search.returnDate')}
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <DatePicker
                  selected={returnDate}
                  onChange={(date) => setValue('returnDate', date!)}
                  minDate={departureDate}
                  dateFormat="dd/MM/yyyy"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}
        </div>

        {/* Passengers and Class */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.passengers')}
            </label>
            <div className="grid grid-cols-3 gap-2">
              <div>
                <label className="block text-xs text-gray-500">{t('common.adults')}</label>
                <input
                  type="number"
                  min="1"
                  max="9"
                  {...register('adults', { min: 1, max: 9 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500">{t('common.children')}</label>
                <input
                  type="number"
                  min="0"
                  max="9"
                  {...register('children', { min: 0, max: 9 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500">{t('common.infants')}</label>
                <input
                  type="number"
                  min="0"
                  max="9"
                  {...register('infants', { min: 0, max: 9 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.class')}
            </label>
            <select
              {...register('cabinClass')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="economy">{t('common.economy')}</option>
              <option value="premium_economy">{t('common.premium_economy')}</option>
              <option value="business">{t('common.business')}</option>
              <option value="first">{t('common.first')}</option>
            </select>
          </div>
        </div>

        {/* Search Button */}
        <button
          type="submit"
          disabled={loading}
          className={cn(
            "w-full bg-blue-600 text-white py-3 px-6 rounded-md font-medium transition-colors",
            "hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            loading && "opacity-50 cursor-not-allowed"
          )}
        >
          <div className="flex items-center justify-center space-x-2">
            <Search className="h-5 w-5" />
            <span>{loading ? t('common.loading') : t('search.searchFlights')}</span>
          </div>
        </button>
      </form>
    </div>
  );
}
