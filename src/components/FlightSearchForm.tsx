'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { Search, Calendar, Users, ArrowUpDown } from 'lucide-react';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { Airport, FlightSearchRequest } from '@/lib/api';
import { cn } from '@/lib/utils';
import AirportSearch from './AirportSearch';

interface FlightSearchFormData {
  originInput: string;
  destinationInput: string;
  originCode: string;
  destinationCode: string;
  departureDate: Date;
  returnDate?: Date;
  adults: number;
  children: number;
  infants: number;
  cabinClass: 'economy' | 'premium_economy' | 'business' | 'first';
  tripType: 'oneWay' | 'roundTrip';
}

interface Props {
  onSearch: (searchData: FlightSearchRequest) => void;
  loading?: boolean;
}

export default function FlightSearchForm({ onSearch, loading = false }: Props) {
  const t = useTranslations();
  const [selectedOrigin, setSelectedOrigin] = useState<Airport | null>(null);
  const [selectedDestination, setSelectedDestination] = useState<Airport | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FlightSearchFormData>({
    defaultValues: {
      originInput: '',
      destinationInput: '',
      originCode: '',
      destinationCode: '',
      adults: 1,
      children: 0,
      infants: 0,
      cabinClass: 'economy',
      tripType: 'roundTrip',
      departureDate: new Date(),
    }
  });

  const tripType = watch('tripType');
  const departureDate = watch('departureDate');
  const returnDate = watch('returnDate');
  const originInput = watch('originInput');
  const destinationInput = watch('destinationInput');

  const handleOriginSelect = (airport: Airport) => {
    setSelectedOrigin(airport);
    setValue('originCode', airport.iata_code);
  };

  const handleDestinationSelect = (airport: Airport) => {
    setSelectedDestination(airport);
    setValue('destinationCode', airport.iata_code);
  };

  const swapAirports = () => {
    const tempOrigin = selectedOrigin;
    const tempDestination = selectedDestination;
    const tempOriginInput = originInput;
    const tempDestinationInput = destinationInput;

    setSelectedOrigin(tempDestination);
    setSelectedDestination(tempOrigin);
    setValue('originInput', tempDestinationInput);
    setValue('destinationInput', tempOriginInput);
    setValue('originCode', tempDestination?.iata_code || '');
    setValue('destinationCode', tempOrigin?.iata_code || '');
  };

  const onSubmit = (data: FlightSearchFormData) => {
    if (!data.originCode || !data.destinationCode) {
      alert('Please select both origin and destination airports');
      return;
    }

    const searchRequest: FlightSearchRequest = {
      passengers: [
        ...Array(data.adults).fill({ type: 'adult' }),
        ...Array(data.children).fill({ type: 'child' }),
        ...Array(data.infants).fill({ type: 'infant' }),
      ],
      slices: [
        {
          origin: data.originCode,
          destination: data.destinationCode,
          departure_date: data.departureDate.toISOString().split('T')[0],
        },
        ...(data.tripType === 'roundTrip' && data.returnDate ? [{
          origin: data.destinationCode,
          destination: data.originCode,
          departure_date: data.returnDate.toISOString().split('T')[0],
        }] : [])
      ],
      cabin_class: data.cabinClass,
    };

    onSearch(searchRequest);
  };

  return (
    <div className="bg-white rounded-xl shadow-xl border border-gray-100 p-6 md:p-8">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Trip Type */}
        <div className="flex space-x-6">
          <label className="flex items-center cursor-pointer group">
            <input
              type="radio"
              value="roundTrip"
              {...register('tripType')}
              className="mr-3 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
            />
            <span className="text-gray-700 group-hover:text-gray-900 font-medium">
              {t('search.roundTrip')}
            </span>
          </label>
          <label className="flex items-center cursor-pointer group">
            <input
              type="radio"
              value="oneWay"
              {...register('tripType')}
              className="mr-3 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
            />
            <span className="text-gray-700 group-hover:text-gray-900 font-medium">
              {t('search.oneWay')}
            </span>
          </label>
        </div>

        {/* Origin and Destination */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative">
          {/* Origin */}
          <div>
            <label className="block text-sm font-semibold text-gray-800 mb-2">
              {t('common.from')}
            </label>
            <AirportSearch
              placeholder={t('search.fromPlaceholder')}
              value={originInput}
              onChange={(value) => setValue('originInput', value)}
              onSelect={handleOriginSelect}
            />
            <input type="hidden" {...register('originCode', { required: true })} />
            {errors.originCode && (
              <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
            )}
          </div>

          {/* Swap Button */}
          <div className="absolute left-1/2 top-8 transform -translate-x-1/2 z-10 hidden md:block">
            <button
              type="button"
              onClick={swapAirports}
              className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
            >
              <ArrowUpDown className="h-4 w-4" />
            </button>
          </div>

          {/* Destination */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.to')}
            </label>
            <AirportSearch
              placeholder={t('search.toPlaceholder')}
              value={destinationInput}
              onChange={(value) => setValue('destinationInput', value)}
              onSelect={handleDestinationSelect}
            />
            <input type="hidden" {...register('destinationCode', { required: true })} />
            {errors.destinationCode && (
              <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
            )}
          </div>
        </div>

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('search.departureDate')}
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <DatePicker
                selected={departureDate}
                onChange={(date) => setValue('departureDate', date!)}
                minDate={new Date()}
                dateFormat="dd/MM/yyyy"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {tripType === 'roundTrip' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('search.returnDate')}
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <DatePicker
                  selected={returnDate}
                  onChange={(date) => setValue('returnDate', date!)}
                  minDate={departureDate}
                  dateFormat="dd/MM/yyyy"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}
        </div>

        {/* Passengers and Class */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.passengers')}
            </label>
            <div className="grid grid-cols-3 gap-2">
              <div>
                <label className="block text-xs text-gray-500">{t('common.adults')}</label>
                <input
                  type="number"
                  min="1"
                  max="9"
                  {...register('adults', { min: 1, max: 9 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500">{t('common.children')}</label>
                <input
                  type="number"
                  min="0"
                  max="9"
                  {...register('children', { min: 0, max: 9 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500">{t('common.infants')}</label>
                <input
                  type="number"
                  min="0"
                  max="9"
                  {...register('infants', { min: 0, max: 9 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.class')}
            </label>
            <select
              {...register('cabinClass')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="economy">{t('common.economy')}</option>
              <option value="premium_economy">{t('common.premium_economy')}</option>
              <option value="business">{t('common.business')}</option>
              <option value="first">{t('common.first')}</option>
            </select>
          </div>
        </div>

        {/* Search Button */}
        <button
          type="submit"
          disabled={loading}
          className={cn(
            "w-full bg-blue-600 text-white py-3 px-6 rounded-md font-medium transition-colors",
            "hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            loading && "opacity-50 cursor-not-allowed"
          )}
        >
          <div className="flex items-center justify-center space-x-2">
            <Search className="h-5 w-5" />
            <span>{loading ? t('common.loading') : t('search.searchFlights')}</span>
          </div>
        </button>
      </form>
    </div>
  );
}
