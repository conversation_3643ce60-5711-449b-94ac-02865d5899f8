import { Plane } from 'lucide-react';

interface Props {
  message?: string;
}

export default function Loading({ message = 'Loading...' }: Props) {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="relative">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <Plane className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-6 w-6 text-blue-600" />
      </div>
      <p className="mt-4 text-gray-600">{message}</p>
    </div>
  );
}
