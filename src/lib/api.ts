import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    total: number;
    provider: string;
  };
}

// Types
export interface Airport {
  id: string;
  name: string;
  city_name: string;
  city: string | null;
  iata_country_code: string;
  iata_code: string;
  icao_code: string;
  iata_city_code: string;
  latitude: number;
  longitude: number;
  time_zone: string;
}

export interface Airline {
  id: string;
  name: string;
  iata_code: string;
  icao_code: string;
}

export interface FlightSearchRequest {
  passengers: Array<{ type: 'adult' | 'child' | 'infant' }>;
  slices: Array<{
    origin: string;
    destination: string;
    departure_date: string;
  }>;
  cabin_class: 'economy' | 'premium_economy' | 'business' | 'first';
}

export interface FlightOffer {
  id: number;
  total_amount: string;
  total_currency: string;
  slices: Array<{
    origin: Airport;
    destination: Airport;
    departure_datetime: string;
    arrival_datetime: string;
    duration: string;
    segments: Array<{
      origin: Airport;
      destination: Airport;
      departure_datetime: string;
      arrival_datetime: string;
      marketing_carrier: Airline;
      aircraft: {
        name: string;
      };
    }>;
  }>;
}

export interface FlightSearchResponse {
  id: number;
  offers: FlightOffer[];
  total_offers: number;
}

// API Functions
export const airportsApi = {
  getAll: () => api.get<ApiResponse<Airport[]>>('/flights/airports'),
  search: (query: string) => api.get<ApiResponse<Airport[]>>(`/flights/airports?query=${query}`),
};

export const airlinesApi = {
  getAll: () => api.get<Airline[]>('/flights/airlines'),
};

export const flightsApi = {
  search: (searchData: FlightSearchRequest) => 
    api.post<{ search_id: number }>('/flights/search', searchData),
  
  getSearchResults: (searchId: number) => 
    api.get<FlightSearchResponse>(`/flights/searches/${searchId}`),
  
  getSearchOffers: (searchId: number, sort?: string, direction?: 'asc' | 'desc') => {
    const params = new URLSearchParams();
    if (sort) params.append('sort', sort);
    if (direction) params.append('direction', direction);
    return api.get<FlightOffer[]>(`/flights/searches/${searchId}/offers?${params}`);
  },
  
  getOfferDetails: (offerId: number) => 
    api.get<FlightOffer>(`/flights/offers/${offerId}`),
};

export const bookingsApi = {
  book: (bookingData: {
    offer_id: number;
    passengers: Array<{
      given_name: string;
      family_name: string;
    }>;
    payments: Array<{
      type: string;
      amount: string;
      currency: string;
    }>;
  }) => api.post('/flights/book', bookingData),
  
  getBooking: (bookingId: number) => api.get(`/flights/bookings/${bookingId}`),
  
  getAllBookings: () => api.get('/flights/bookings'),
  
  cancelBooking: (bookingId: number) => api.post(`/flights/bookings/${bookingId}/cancel`),
};
